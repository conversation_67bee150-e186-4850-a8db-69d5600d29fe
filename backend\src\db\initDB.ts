const { Pool } = require('pg');
const dotenv = require('dotenv');
dotenv.config();

const pool = new Pool({
  user: process.env.PG_USER,
  host: process.env.PG_HOST,
  database: process.env.PG_DATABASE,
  password: process.env.PG_PASSWORD,
  port: Number(process.env.PG_PORT),
});

async function createTables() {
  await pool.query(`
    CREATE TABLE IF NOT EXISTS items (
      id INT PRIMARY KEY,
      name TEXT NOT NULL,
      members BOOLEAN NOT NULL,
      max_limit INT,
      value NUMERIC NOT NULL,
      highalch NUMERIC,
      lowalch NUMERIC,
      icon TEXT
    )
  `);

  await pool.query(`
    CREATE TABLE IF NOT EXISTS item_prices (
      item_id INT REFERENCES items(id),
      current_price NUMERIC,
      current_trend TEXT,
      today_price NUMERIC,
      today_trend TEXT,
      volume BIGINT,
      fetched_at TIMESTAMP DEFAULT NOW(),
      PRIMARY KEY(item_id)
    )
  `);

  console.log("Tabelas criadas ou já existentes");
}

// chamar a função
createTables().catch(console.error);
