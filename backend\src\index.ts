import express = require("express");
import cors = require("cors");
const itemsRouter = require("./routes/items");
const { initDB } = require("./db/initDB");
const { populateItems } = require("./db/populateItems");
const { populatePrices } = require("./db/populatePrices");
const { populateVolumes } = require("./db/populateVolumes");
import type { Application } from "express";

const app: Application = express();
const PORTDB = process.env.PORT || 3000;
const PORT = 3001;

// Middlewares
app.use(cors());
app.use(express.json());

(async () => {
  try {
    await initDB();
    await populateItems();
    await populatePrices();
    await populateVolumes();
    app.use(express.json());
    app.use("/items", itemsRouter);

    app.listen(PORT, () => {
      console.log(`Database running on port: ${PORTDB}`);
    });
  } catch (err) {
    console.error("Erro ao iniciar o DB:", err);
    process.exit(1);
  }
})();

// Routes
app.use("/items", itemsRouter);

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
});
