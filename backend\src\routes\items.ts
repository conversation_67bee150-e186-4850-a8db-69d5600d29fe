import type express from "express";
const router = express.Router();
import type Item = require("../models/Item");

const items: Item.ItemStatic[] = [
  {
    id: 4151,
    name: "Abyssal whip",
    members: true,
    limit: 70,
    value: 120001,
    highalch: 72000,
    lowalch: 48000,
    icon: "Abyssal whip.png",
    current: { price: 120001, trend: "positive" },
    today: { price: 120001, trend: "positive" },
    volume: 512
  },
  {
    id: 4587,
    name: "Dragon scimitar",
    members: true,
    limit: 1,
    value: 100000,
    highalch: 72000,
    lowalch: 48000,
    icon: "Dragon scimitar.png",
    current: { price: 100000, trend: "negative" },
    today: { price: 100000, trend: "negative" },
    volume: 128
  }
];

router.get("/", (req: Express.Request, res: Express.Response) => {
  res.json(items);
});

router.get("/search", async (req: Express.Request, res: Express.Response) => {
  const API_BASE = "https://grandexchange.tools";

  const query = (req.query.q as string);
  if (!query)
    return res.status(400).json({ error: "Parameter 'q' is required." });

  try {
    console.log(`${API_BASE}/api/items?name=${encodeURIComponent(query)}`);
    const apiRes = await fetch(
      `${API_BASE}/api/items?name=${encodeURIComponent(query)}`
    );
    const data = await apiRes.json();
    const itemsArray = data.items || [];

    const results = itemsArray.filter((item: any) =>
      item.name.toLowerCase().includes(query.toLowerCase())
    );

    const formattedResults = results.map((i: any) => ({
      id: i.id,
      name: i.name,
      price: i.value,
      icon: i.icon
    }));

    res.json(formattedResults);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Erro ao buscar API externa" });
  }
});
module.exports = router;